# Product Materials Update

## Overview
This update adds support for using both raw materials (matière première) and semi-finished products (semi-fini) when creating finished products (produits finis).

## Database Changes

### 1. Products Table
- Added `type` column to distinguish between different product types:
  - `matiere`: Raw materials
  - `semi`: Semi-finished products  
  - `finis`: Finished products

### 2. Product Materials Table
- Added `material_type` column to specify the type of material being used:
  - `matiere`: Raw material
  - `semi`: Semi-finished product

## API Changes

### New Endpoint
- `GET /api/available-materials`: Returns all available materials (both raw materials and semi-finished products)

### Updated Endpoints
- `POST /api/products-with-materials`: Now supports `materialType` field in materials array
- `GET /api/products/:id/materials`: Now returns `material_type` field

## Frontend Changes

### ProductMaterials.tsx
- Updated material selection to include both raw materials and semi-finished products
- Added material type selector in add/edit material modals
- Updated material display to show material type
- Material dropdowns now filter by selected type

## Migration Instructions

1. Run the migration script to add missing columns:
   ```sql
   -- Add type column to products table
   ALTER TABLE products ADD COLUMN IF NOT EXISTS type VARCHAR(50) NOT NULL DEFAULT 'matiere';
   
   -- Add material_type column to product_materials table  
   ALTER TABLE product_materials ADD COLUMN IF NOT EXISTS material_type VARCHAR(50) NOT NULL DEFAULT 'matiere';
   
   -- Add indexes
   CREATE INDEX IF NOT EXISTS idx_products_type ON products(type);
   CREATE INDEX IF NOT EXISTS idx_product_materials_material_type ON product_materials(material_type);
   ```

2. Restart the backend server to load the updated API endpoints

## Usage

When creating a finished product, you can now:
1. Select "Fini" as the product type
2. Add materials by selecting either "Matière première" or "Semi-fini" as the material type
3. Choose from the filtered list of available materials based on the selected type
4. The system will track the material type and display it in the materials list

## Benefits

- More flexible BOM (Bill of Materials) structure
- Support for complex product hierarchies
- Better tracking of material types in finished products
- Improved user experience with filtered material selection 