import { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Eye, EyeOff, Mail, Lock } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from 'sonner';
import { saveUserSession } from '@/lib/session';
// Removed: import { useAuth } from '@/contexts/AuthContext';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const isMobile = useIsMobile();
  // Removed: const { signIn } = useAuth();

  // Get redirect URL from query params
  const redirectTo = searchParams.get('redirect') || '/';
  const isVerified = searchParams.get('verified') === 'true';

  // Show success message if user just verified their email
  useEffect(() => {
    if (isVerified) {
      toast.success('Email verified successfully! You can now log in to your account.');
      // Remove the verified parameter from URL
      navigate('/login', { replace: true });
    }
  }, [isVerified, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!email || !password) {
      toast.error('Please fill in all fields');
      return;
    }

    setIsLoading(true);

    // Hardcoded admin login
    setTimeout(() => {
      if (email === '<EMAIL>' && password === 'admin') {
        // Save session for hardcoded admin
        saveUserSession(
          { id: '1', email: '<EMAIL>' },
          {
            id: '1',
            name: 'Admin',
            email: '<EMAIL>',
            role: 'admin',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }
        );
        toast.success('Login successful!');
        navigate(redirectTo, { replace: true });
      } else {
        toast.error('Invalid credentials. <NAME_EMAIL>/admin.');
      }
      setIsLoading(false);
    }, 700);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      {/* Left side - Image (hidden on small mobile) */}
      {/* Removed left image section for full centering */}
      {/* Right side - Login Form */}
      <Card className="w-full max-w-md md:shadow-lg md:border-0 border-0 shadow-none">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-6">
            <img
              src="/friction.png"
              alt="Logo"
              className="w-16 h-16 object-cover"
            />
          </div>
          {/* Removed Admin Panel and CardDescription */}
          <CardTitle className="text-2xl font-bold text-center">Sign in</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  className="pl-10"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="password">Password</Label>
                <Link to="/forgot-password" className="text-sm text-accent hover:underline">
                  Forgot password?
                </Link>
              </div>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  className="pl-10 pr-10"
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full px-3 py-2 text-muted-foreground"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="remember" 
                checked={rememberMe}
                onCheckedChange={(checked) => setRememberMe(checked as boolean)}
              />
              <label
                htmlFor="remember"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Remember me
              </label>
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? 'Signing in...' : 'Sign in'}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
            {/* <div className="text-center text-sm">
              Don't have an account?{' '}
              <Link to="/register" className="text-accent font-medium hover:underline">
                Sign up
              </Link>
            </div> */}
          </CardFooter>
      </Card>
    </div>
  );
};

export default Login;
