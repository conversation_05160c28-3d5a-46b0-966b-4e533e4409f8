import React, { useState, useEffect } from 'react';
import { Bell, Search, Moon, Sun, Menu, Package, CreditCard, Recycle } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useTheme } from "@/hooks/use-theme";
import { Badge } from "@/components/ui/badge";
import { useIsMobile } from "@/hooks/use-mobile";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { getUserInitials } from "@/lib/session";
import { Dialog, DialogContent, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';

interface HeaderProps {
  toggleSidebar: () => void;
}

interface Notification {
  id: number;
  product_id: number;
  message: string;
  is_read: boolean;
  created_at: string;
  product_name: string;
}

const Header = ({ toggleSidebar }: HeaderProps) => {
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const { userProfile, user, signOut } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [showRecycleDialog, setShowRecycleDialog] = useState(false);
  const [recycleLoading, setRecycleLoading] = useState(false);

  const unreadCount = notifications.filter(n => !n.is_read).length;

  const { theme, setTheme } = useTheme();

  // Fetch all notifications (not just unread)
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const res = await fetch('/api/notifications?all=1');
      if (!res.ok) throw new Error('Failed to fetch notifications');
      const data = await res.json();
      setNotifications(data);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch notifications on component mount
  useEffect(() => {
    fetchNotifications();
    
    // Set up interval to refresh notifications every 30 seconds
    const interval = setInterval(fetchNotifications, 30000);
    
    return () => clearInterval(interval);
  }, []);

  // Toggle read/unread status
  const toggleRead = async (id: number, is_read: boolean) => {
    try {
      await fetch(`/api/notifications/${id}/${is_read ? 'unread' : 'read'}`, { method: 'POST' });
      setNotifications(notifications.map(n => n.id === id ? { ...n, is_read: !is_read } : n));
    } catch (error) {
      console.error('Failed to toggle notification read status:', error);
    }
  };

  // Mark all as read
  const markAllAsRead = async () => {
    try {
      await Promise.all(
        notifications.filter(n => !n.is_read).map(n =>
          fetch(`/api/notifications/${n.id}/read`, { method: 'POST' })
        )
      );
      setNotifications(notifications.map(n => ({ ...n, is_read: true })));
    } catch (error) {
      console.error('Failed to mark all as read:', error);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const handleLogout = async () => {
    try {
      await signOut();
      toast.success('Logged out successfully');
      navigate('/login');
    } catch (error) {
      toast.error('Failed to logout. Please try again.');
    }
  };

  const handleRecycle = async () => {
    setRecycleLoading(true);
    try {
      const res = await fetch('/api/admin/reset-movements-products', { method: 'POST' });
      if (!res.ok) throw new Error('Failed to reset');
      // Also reset notifications
      await fetch('/api/admin/reset-notifications', { method: 'POST' });
      toast.success('Toutes les données de mouvement et notifications ont été supprimées et les stocks remis à zéro.');
      setShowRecycleDialog(false);
      fetchNotifications();
    } catch (e) {
      toast.error('Erreur lors de la réinitialisation.');
    } finally {
      setRecycleLoading(false);
    }
  };

  // Get user display data with fallbacks
  const userName = userProfile?.name || user?.email?.split('@')[0] || 'User';
  const userEmail = userProfile?.email || user?.email || '';
  const userInitials = getUserInitials(userName);

  return (
    <header className="w-full h-16 border-b border-border bg-background flex items-center justify-between px-4 sm:px-6">
      {/* Left side with menu toggle on mobile */}
      <div className="flex items-center gap-3">
        {isMobile && (
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleSidebar}
            className="md:hidden text-muted-foreground hover:text-foreground"
            aria-label="Toggle sidebar menu"
          >
            <Menu size={22} />
          </Button>
        )}

        {/* Search Bar - hidden on small mobile screens */}
        <div className="relative max-w-md w-full hidden sm:block">
          <Search className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground" size={18} />
          <Input
            placeholder="Search products, orders, customers..."
            className="pl-9 w-full bg-muted/40"
          />
        </div>
      </div>

      {/* Right Side Actions */}
      <div className="flex items-center gap-3 sm:gap-4">
        {/* Theme Toggle */}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
          className="text-muted-foreground hover:text-foreground"
        >
          {theme === "dark" ? <Sun size={20} /> : <Moon size={20} />}
        </Button>

        {/* Recycle/Reset Button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setShowRecycleDialog(true)}
          className="text-muted-foreground hover:text-red-600"
          aria-label="Réinitialiser les mouvements et stocks"
        >
          <Recycle size={20} />
        </Button>

        {/* Notifications */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="relative">
              <Bell size={20} />
              {unreadCount > 0 && (
                <Badge variant="destructive" className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center text-xs">
                  {unreadCount}
                </Badge>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-80">
            <div className="p-2 text-sm font-medium border-b flex items-center justify-between">
              <span>Notifications</span>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={fetchNotifications}
                  disabled={loading}
                  className="h-6 px-2 text-xs"
                >
                  {loading ? 'Loading...' : 'Refresh'}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={markAllAsRead}
                  disabled={loading || notifications.every(n => n.is_read)}
                  className="h-6 px-2 text-xs"
                >
                  Mark all as read
                </Button>
              </div>
            </div>
            <div className="max-h-[300px] overflow-y-auto">
              {notifications.length > 0 ? (
                notifications.map(notification => (
                  <DropdownMenuItem
                    key={notification.id}
                    onClick={() => toggleRead(notification.id, notification.is_read)}
                    className={`p-3 cursor-pointer ${!notification.is_read ? 'bg-muted/30 font-semibold' : ''}`}
                  >
                    <div className="flex items-start gap-3">
                      <div className={`h-2 w-2 mt-1.5 rounded-full ${!notification.is_read ? 'bg-primary' : 'bg-muted'}`}></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{notification.product_name}</p>
                        <p className="text-xs text-muted-foreground">{notification.message}</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {formatTimestamp(notification.created_at)}
                        </p>
                      </div>
                      <span className="ml-2 text-xs text-blue-600 cursor-pointer select-none">
                        {!notification.is_read ? 'Mark as read' : 'Mark as unread'}
                      </span>
                    </div>
                  </DropdownMenuItem>
                ))
              ) : (
                <div className="p-4 text-center text-muted-foreground">
                  No notifications
                </div>
              )}
            </div>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* User Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Avatar className="cursor-pointer">
              <AvatarFallback className="bg-primary text-primary-foreground font-medium">
                {userInitials}
              </AvatarFallback>
            </Avatar>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <div className="p-2 border-b">
              <p className="font-medium">{userName}</p>
              <p className="text-sm text-muted-foreground">{userEmail}</p>
            </div>
            <DropdownMenuItem onClick={() => navigate('/profile')}>My Profile</DropdownMenuItem>
            <DropdownMenuItem onClick={() => navigate('/settings')}>Settings</DropdownMenuItem>
            <DropdownMenuItem>Help & Support</DropdownMenuItem>
            <DropdownMenuItem className="text-destructive" onClick={handleLogout}>Logout</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Recycle Confirmation Dialog */}
      <Dialog open={showRecycleDialog} onOpenChange={setShowRecycleDialog}>
        <DialogContent>
          <DialogTitle>Réinitialiser les mouvements et stocks ?</DialogTitle>
          <DialogDescription>
            Cette action supprimera <b>toutes</b> les données de mouvement et remettra à zéro le stock, total entrées et total sorties de tous les produits. Cette action est irréversible.
          </DialogDescription>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRecycleDialog(false)} disabled={recycleLoading}>
              Annuler
            </Button>
            <Button variant="destructive" onClick={handleRecycle} disabled={recycleLoading}>
              {recycleLoading ? 'Réinitialisation...' : 'Confirmer'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </header>
  );
};

export default Header;
