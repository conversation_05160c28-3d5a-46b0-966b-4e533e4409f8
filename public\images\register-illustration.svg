<svg xmlns="http://www.w3.org/2000/svg" data-name="Layer 1" width="786.81995" height="572.25773" viewBox="0 0 786.81995 572.25773" xmlns:xlink="http://www.w3.org/1999/xlink"><circle cx="458.32136" cy="74.18162" r="26.70075" fill="#f2f2f2"/><path d="M872.03753,661.02307c39.6877-30.22889,58.64352-81.02546,46.77635-129.48234-11.86717-48.45687-54.59773-83.7618-102.74928-91.45011l2.08857,34.99363-15.99249-37.291A103.93673,103.93673,0,0,0,769.34005,449.508a105.507,105.507,0,0,0-69.5945,119.23471c11.86717,48.45688,54.59773,83.76181,102.74929,91.45011l-2.08858-34.99363,15.99249,37.291A103.93671,103.93671,0,0,0,849.0947,650.74379,104.95929,104.95929,0,0,0,872.03753,661.02307Z" transform="translate(-206.59003 -163.87113)" fill="#f2f2f2"/><rect x="122.20997" y="392.87113" width="664.60998" height="2" fill="#3f3d56"/><path d="M624.59,197.87h-74v-34h74Zm-72-2h70v-30h-70Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M790.59,197.87h-74v-34h74Zm-72-2h70v-30h-70Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M880.59,197.87h-74v-34h74Zm-72-2h70v-30h-70Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M573.59,350.87a94,94,0,0,0-94,94c0,1.84.05,3.67.14,5.49h34.74a59.66,59.66,0,0,1-.88-10.49,60,60,0,0,1,120,0,59.66,59.66,0,0,1-.88,10.49h34.74c.09-1.82.14-3.65.14-5.49A94,94,0,0,0,573.59,350.87Z" transform="translate(-206.59003 -163.87113)" fill="#2563eb"/><path d="M598.59,444.87a25,25,0,1,1,25-25A25,25,0,0,1,598.59,444.87Zm0-48a23,23,0,1,0,23,23A23,23,0,0,0,598.59,396.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><rect x="117.50997" y="494.37113" width="16" height="2" fill="#3f3d56"/><rect x="117.50997" y="500.37113" width="16" height="2" fill="#3f3d56"/><rect x="117.50997" y="506.37113" width="16" height="2" fill="#3f3d56"/><rect x="117.50997" y="512.37113" width="16" height="2" fill="#3f3d56"/><rect x="117.50997" y="518.37113" width="16" height="2" fill="#3f3d56"/><rect x="117.50997" y="524.37113" width="16" height="2" fill="#3f3d56"/><rect x="117.50997" y="530.37113" width="16" height="2" fill="#3f3d56"/><path d="M775.59,436.87h-184v-26h184Zm-182-2h180v-22h-180Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><rect x="447.50997" y="303.87113" width="2" height="14" fill="#3f3d56"/><path d="M654.59,481.87a1,1,0,0,1-1-1v-49a1,1,0,0,1,2,0v49A1,1,0,0,1,654.59,481.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><rect x="486.50997" y="277.37113" width="2" height="14" fill="#3f3d56"/><path d="M693.59,455.37a1,1,0,0,1-1-1v-49a1,1,0,0,1,2,0v49A1,1,0,0,1,693.59,455.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><rect x="651.50997" y="303.87113" width="2" height="14" fill="#3f3d56"/><path d="M858.59,481.87a1,1,0,0,1-1-1v-49a1,1,0,0,1,2,0v49A1,1,0,0,1,858.59,481.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><rect x="690.50997" y="277.37113" width="2" height="14" fill="#3f3d56"/><path d="M897.59,455.37a1,1,0,0,1-1-1v-49a1,1,0,0,1,2,0v49A1,1,0,0,1,897.59,455.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M856.59,272.87a10,10,0,1,1,10-10A10,10,0,0,1,856.59,272.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,856.59,254.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M856.59,304.87a10,10,0,1,1,10-10A10,10,0,0,1,856.59,304.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,856.59,286.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M765.59,272.87a10,10,0,1,1,10-10A10,10,0,0,1,765.59,272.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,765.59,254.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M765.59,304.87a10,10,0,1,1,10-10A10,10,0,0,1,765.59,304.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,765.59,286.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M673.59,272.87a10,10,0,1,1,10-10A10,10,0,0,1,673.59,272.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,673.59,254.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M673.59,304.87a10,10,0,1,1,10-10A10,10,0,0,1,673.59,304.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,673.59,286.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M584.59,272.87a10,10,0,1,1,10-10A10,10,0,0,1,584.59,272.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,584.59,254.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M584.59,304.87a10,10,0,1,1,10-10A10,10,0,0,1,584.59,304.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,584.59,286.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M635.59,364.87a10,10,0,1,1,10-10A10,10,0,0,1,635.59,364.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,635.59,346.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M635.59,396.87a10,10,0,1,1,10-10A10,10,0,0,1,635.59,396.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,635.59,378.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M815.59,364.87a10,10,0,1,1,10-10A10,10,0,0,1,815.59,364.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,815.59,346.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M815.59,396.87a10,10,0,1,1,10-10A10,10,0,0,1,815.59,396.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,815.59,378.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M709.59,446.87a10,10,0,1,1,10-10A10,10,0,0,1,709.59,446.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,709.59,428.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M739.59,446.87a10,10,0,1,1,10-10A10,10,0,0,1,739.59,446.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,739.59,428.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M769.59,446.87a10,10,0,1,1,10-10A10,10,0,0,1,769.59,446.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,769.59,428.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M654.59,272.87a10,10,0,1,1,10-10A10,10,0,0,1,654.59,272.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,654.59,254.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M654.59,304.87a10,10,0,1,1,10-10A10,10,0,0,1,654.59,304.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,654.59,286.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M693.59,272.87a10,10,0,1,1,10-10A10,10,0,0,1,693.59,272.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,693.59,254.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M693.59,304.87a10,10,0,1,1,10-10A10,10,0,0,1,693.59,304.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,693.59,286.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M731.59,272.87a10,10,0,1,1,10-10A10,10,0,0,1,731.59,272.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,731.59,254.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M731.59,304.87a10,10,0,1,1,10-10A10,10,0,0,1,731.59,304.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,731.59,286.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M701.59,364.87a10,10,0,1,1,10-10A10,10,0,0,1,701.59,364.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,701.59,346.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M701.59,396.87a10,10,0,1,1,10-10A10,10,0,0,1,701.59,396.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,701.59,378.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M751.59,364.87a10,10,0,1,1,10-10A10,10,0,0,1,751.59,364.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,751.59,346.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M751.59,396.87a10,10,0,1,1,10-10A10,10,0,0,1,751.59,396.87Zm0-18a8,8,0,1,0,8,8A8,8,0,0,0,751.59,378.87Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M489.59,599.37a1,1,0,0,1-1-1v-48a1,1,0,0,1,2,0v48A1,1,0,0,1,489.59,599.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M487.59,555.37a1,1,0,0,1-.707-1.707l12-12a1,1,0,0,1,1.414,1.414l-12,12A.99676.99676,0,0,1,487.59,555.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M499.59,555.37a.99676.99676,0,0,1-.707-.293l-12-12a1,1,0,0,1,1.414-1.414l12,12a1,1,0,0,1-.707,1.707Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M903.59,599.37a1,1,0,0,1-1-1v-48a1,1,0,0,1,2,0v48A1,1,0,0,1,903.59,599.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M901.59,555.37a1,1,0,0,1-.707-1.707l12-12a1,1,0,0,1,1.414,1.414l-12,12A.99676.99676,0,0,1,901.59,555.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M913.59,555.37a.99676.99676,0,0,1-.707-.293l-12-12a1,1,0,0,1,1.414-1.414l12,12a1,1,0,0,1-.707,1.707Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M695.59,599.37a1,1,0,0,1-1-1v-48a1,1,0,0,1,2,0v48A1,1,0,0,1,695.59,599.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M693.59,555.37a1,1,0,0,1-.707-1.707l12-12a1,1,0,0,1,1.414,1.414l-12,12A.99676.99676,0,0,1,693.59,555.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M705.59,555.37a.99676.99676,0,0,1-.707-.293l-12-12a1,1,0,0,1,1.414-1.414l12,12a1,1,0,0,1-.707,1.707Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M695.59,599.37a1,1,0,0,1-1-1v-48a1,1,0,0,1,2,0v48A1,1,0,0,1,695.59,599.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M693.59,555.37a1,1,0,0,1-.707-1.707l12-12a1,1,0,0,1,1.414,1.414l-12,12A.99676.99676,0,0,1,693.59,555.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M705.59,555.37a.99676.99676,0,0,1-.707-.293l-12-12a1,1,0,0,1,1.414-1.414l12,12a1,1,0,0,1-.707,1.707Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M695.59,599.37a1,1,0,0,1-1-1v-48a1,1,0,0,1,2,0v48A1,1,0,0,1,695.59,599.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M693.59,555.37a1,1,0,0,1-.707-1.707l12-12a1,1,0,0,1,1.414,1.414l-12,12A.99676.99676,0,0,1,693.59,555.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M705.59,555.37a.99676.99676,0,0,1-.707-.293l-12-12a1,1,0,0,1,1.414-1.414l12,12a1,1,0,0,1-.707,1.707Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M695.59,599.37a1,1,0,0,1-1-1v-48a1,1,0,0,1,2,0v48A1,1,0,0,1,695.59,599.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M693.59,555.37a1,1,0,0,1-.707-1.707l12-12a1,1,0,0,1,1.414,1.414l-12,12A.99676.99676,0,0,1,693.59,555.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M705.59,555.37a.99676.99676,0,0,1-.707-.293l-12-12a1,1,0,0,1,1.414-1.414l12,12a1,1,0,0,1-.707,1.707Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M695.59,599.37a1,1,0,0,1-1-1v-48a1,1,0,0,1,2,0v48A1,1,0,0,1,695.59,599.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M693.59,555.37a1,1,0,0,1-.707-1.707l12-12a1,1,0,0,1,1.414,1.414l-12,12A.99676.99676,0,0,1,693.59,555.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M705.59,555.37a.99676.99676,0,0,1-.707-.293l-12-12a1,1,0,0,1,1.414-1.414l12,12a1,1,0,0,1-.707,1.707Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M695.59,599.37a1,1,0,0,1-1-1v-48a1,1,0,0,1,2,0v48A1,1,0,0,1,695.59,599.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M693.59,555.37a1,1,0,0,1-.707-1.707l12-12a1,1,0,0,1,1.414,1.414l-12,12A.99676.99676,0,0,1,693.59,555.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M705.59,555.37a.99676.99676,0,0,1-.707-.293l-12-12a1,1,0,0,1,1.414-1.414l12,12a1,1,0,0,1-.707,1.707Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M695.59,599.37a1,1,0,0,1-1-1v-48a1,1,0,0,1,2,0v48A1,1,0,0,1,695.59,599.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M693.59,555.37a1,1,0,0,1-.707-1.707l12-12a1,1,0,0,1,1.414,1.414l-12,12A.99676.99676,0,0,1,693.59,555.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M705.59,555.37a.99676.99676,0,0,1-.707-.293l-12-12a1,1,0,0,1,1.414-1.414l12,12a1,1,0,0,1-.707,1.707Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M695.59,599.37a1,1,0,0,1-1-1v-48a1,1,0,0,1,2,0v48A1,1,0,0,1,695.59,599.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M693.59,555.37a1,1,0,0,1-.707-1.707l12-12a1,1,0,0,1,1.414,1.414l-12,12A.99676.99676,0,0,1,693.59,555.37Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/><path d="M705.59,555.37a.99676.99676,0,0,1-.707-.293l-12-12a1,1,0,0,1,1.414-1.414l12,12a1,1,0,0,1-.707,1.707Z" transform="translate(-206.59003 -163.87113)" fill="#3f3d56"/></svg>
