import QRCode from 'qrcode';

export interface QRCodeData {
  productId: number;
  productName: string;
  productReference: string;
  batchNumber: string;
  quantity: number;
  fabricationDate: string;
  expirationDate: string;
  supplier: string;
  location: string;
  floors: Array<{
    name: string;
    quantity: number;
  }>;
  operationType: string;
  timestamp: string;
}

export const generateQRCodeData = (data: QRCodeData): string => {
  // Create a structured JSON string with all the data
  return JSON.stringify({
    productId: data.productId,
    productName: data.productName,
    productReference: data.productReference,
    batchNumber: data.batchNumber,
    quantity: data.quantity,
    fabricationDate: data.fabricationDate,
    expirationDate: data.expirationDate,
    supplier: data.supplier,
    location: data.location,
    floors: data.floors,
    operationType: data.operationType,
    timestamp: data.timestamp,
    // Add a verification hash or ID for security
    hash: btoa(`${data.productId}-${data.batchNumber}-${data.timestamp}`)
  });
};

export const generateQRCodeImage = async (data: QRCodeData): Promise<string> => {
  try {
    const qrData = generateQRCodeData(data);
    
    // Generate QR code as data URL
    const qrCodeDataURL = await QRCode.toDataURL(qrData, {
      width: 200,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });
    
    return qrCodeDataURL;
  } catch (error) {
    console.error('Error generating QR code:', error);
    throw new Error('Failed to generate QR code');
  }
};

export const generateQRCodeSVG = async (data: QRCodeData): Promise<string> => {
  try {
    const qrData = generateQRCodeData(data);
    
    // Generate QR code as SVG string
    const qrCodeSVG = await QRCode.toString(qrData, {
      type: 'svg',
      width: 200,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });
    
    return qrCodeSVG;
  } catch (error) {
    console.error('Error generating QR code SVG:', error);
    throw new Error('Failed to generate QR code SVG');
  }
};