import React, { createContext, useContext, useEffect, useState } from 'react';
import { getUserSession } from '../lib/session';

interface UserProfile {
  id: string;
  name: string;
  role: string;
  phone?: string;
  created_at?: string;
  updated_at?: string;
}

interface AuthContextType {
  user: any | null; // Changed from User | null
  session: any | null; // Changed from Session | null
  profile: UserProfile | null;
  loading: boolean;
  profileLoading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, userData?: { name: string; role: string }) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  refreshProfile: () => Promise<void>;
  updateProfile: (profileData: Partial<UserProfile>) => Promise<{ error: any }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<any | null>(null); // Changed from User | null
  const [session, setSession] = useState<any | null>(null); // Changed from Session | null
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [profileLoading, setProfileLoading] = useState(false);

  // Function to fetch user profile
  const fetchUserProfile = async (userId: string) => {
    try {
      setProfileLoading(true);
      // const profileData = await getProfile(userId); // Removed getProfile
      // console.log('Fetched profile data:', profileData);
      // setProfile(profileData);
      // Placeholder for actual profile fetching logic
      setProfile({ id: userId, name: 'User', role: 'User' }); // Example placeholder
    } catch (error) {
      console.error('Error fetching user profile:', error);
      setProfile(null);
    } finally {
      setProfileLoading(false);
    }
  };

  useEffect(() => {
    // Get initial session
    // Remove: supabase.auth.getSession().then(({ data: { session } }) => {
    // Remove: setSession(session);
    // Remove: setUser(session?.user ?? null);

    // Fetch profile if user exists
    // Remove: if (session?.user) {
    // Remove: fetchUserProfile(session.user.id);
    // Remove: } else {
    // Remove: // Fallback: check local session
    // Remove: const localSession = getUserSession();
    // Remove: if (localSession) {
    // Remove: setUser({ id: localSession.user.id, email: localSession.user.email } as any);
    // Remove: setProfile(localSession.profile);
    // Remove: } else {
    // Remove: setProfile(null);
    // Remove: }
    // Remove: }

    // setLoading(false);

    // Listen for auth changes
    // Remove: const {
    // Remove:   data: { subscription },
    // Remove: } = supabase.auth.onAuthStateChange((_event, session) => {
    // Remove:   setSession(session);
    // Remove:   setUser(session?.user ?? null);

    // Fetch profile if user exists, clear if not
    // Remove:   if (session?.user) {
    // Remove:     fetchUserProfile(session.user.id);
    // Remove:   } else {
    // Remove:     // Fallback: check local session
    // Remove:     const localSession = getUserSession();
    // Remove:     if (localSession) {
    // Remove:       setUser({ id: localSession.user.id, email: localSession.user.email } as any);
    // Remove:       setProfile(localSession.profile);
    // Remove:     } else {
    // Remove:       setProfile(null);
    // Remove:     }
    // Remove:   }

    // setLoading(false);
    // });

    // return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    // Remove: const { error } = await supabase.auth.signInWithPassword({
    // Remove:   email,
    // Remove:   password,
    // Remove: });
    return { error: null }; // Placeholder, no Supabase auth
  };

  const signUp = async (email: string, password: string, userData?: { name: string; role: string }) => {
    try {
      // First, create the user in Supabase Auth
      // Remove: const { data, error: authError } = await supabase.auth.signUp({
      // Remove:   email,
      // Remove:   password,
      // Remove: });

      // if (authError) {
      // Remove:   return { error: authError };
      // Remove: }

      // If user creation was successful and we have user data, create the profile
      // Remove: if (data.user && userData) {
      // Remove:   const { error: profileError } = await supabase
      // Remove:     .from('profiles')
      // Remove:     .insert({
      // Remove:       id: data.user.id,
      // Remove:       name: userData.name,
      // Remove:       role: userData.role,
      // Remove:     });

      // if (profileError) {
      // Remove:   console.error('Profile creation error:', profileError);
      // Remove:   // Note: We don't return this error as the user was created successfully
      // Remove:   // The profile can be created later if needed
      // Remove: }
      // Remove: }

      return { error: null }; // Placeholder, no Supabase auth
    } catch (error) {
      return { error };
    }
  };

  const signOut = async () => {
    try {
      // Sign out from Supabase (this automatically clears tokens from localStorage)
      // Remove: await supabase.auth.signOut();

      // Clear profile state
      setProfile(null);

      // Additional cleanup: Remove any custom tokens or auth-related data from localStorage
      // Note: Supabase automatically handles its own token cleanup, but we ensure any custom tokens are removed
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('supabase') || key.includes('auth') || key.includes('token'))) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));

      // Also clear sessionStorage for any auth-related data
      const sessionKeysToRemove = [];
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && (key.includes('supabase') || key.includes('auth') || key.includes('token'))) {
          sessionKeysToRemove.push(key);
        }
      }
      sessionKeysToRemove.forEach(key => sessionStorage.removeItem(key));

    } catch (error) {
      console.error('Error during logout:', error);
      // Even if there's an error, we should still clear local state
      setProfile(null);
      throw error;
    }
  };

  // Function to refresh user profile
  const refreshProfile = async () => {
    if (user) {
      await fetchUserProfile(user.id);
    }
  };

  // Function to update user profile
  const updateProfile = async (profileData: Partial<UserProfile>) => {
    if (!user) {
      return { error: { message: 'No authenticated user' } };
    }

    try {
      setProfileLoading(true);
      // Remove: const { data, error } = await supabase
      // Remove:   .from('profiles')
      // Remove:   .update(profileData)
      // Remove:   .eq('id', user.id)
      // Remove:   .select()
      // Remove:   .single();

      // if (error) {
      // Remove:   return { error };
      // Remove: }

      // Update local profile state
      return { error: null };
    } catch (error) {
      return { error };
    } finally {
      setProfileLoading(false);
    }
  };

  const value = {
    user,
    session,
    profile,
    loading,
    profileLoading,
    signIn,
    signUp,
    signOut,
    refreshProfile,
    updateProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
