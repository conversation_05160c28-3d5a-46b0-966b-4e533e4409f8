const express = require('express');
const router = express.Router();
const db = require('../db');

// Get all quality movements
router.get('/', (req, res) => {
  const query = `
    SELECT 
      m.*,
      p.nom AS product_name,
      p.reference AS product_reference,
      p.unite,
      l.name AS location_name,
      e.name AS etage_name,
      pt.name AS part_name,
      DATE_FORMAT(m.fabrication_date, '%Y-%m-%d') as fabrication_date,
      DATE_FORMAT(m.expiration_date, '%Y-%m-%d') as expiration_date
    FROM movements m
    LEFT JOIN products p ON m.product_id = p.id
    LEFT JOIN locations l ON m.location_id = l.id
    LEFT JOIN location_etages e ON m.etage_id = e.id
    LEFT JOIN location_parts pt ON m.part_id = pt.id
    WHERE m.status = 'Entrée'
    ORDER BY m.date DESC, m.time DESC
  `;

  db.query(query, (err, results) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ error: 'Failed to fetch quality movements', details: err });
    }
    console.log('Fetched movements:', results);
    res.json(results);
  });
});

// Update quality status
router.patch('/:id', (req, res) => {
  const { id } = req.params;
  const { quality_status } = req.body;

  if (!quality_status || !['conforme', 'non-conforme'].includes(quality_status)) {
    return res.status(400).json({ error: 'Invalid quality status' });
  }

  const now = new Date();
  const updatedAt = now.toISOString().slice(0, 19).replace('T', ' '); // Format: YYYY-MM-DD HH:MM:SS

  db.query(
    'UPDATE movements SET quality_status = ?, updated_at = ? WHERE id = ?',
    [quality_status, updatedAt, id],
    (err, result) => {
      if (err) {
        console.error('Error updating quality status:', err);
        return res.status(500).json({ error: 'Failed to update quality status' });
      }

      if (result.affectedRows === 0) {
        return res.status(404).json({ error: 'Movement not found' });
      }

      res.json({ 
        success: true, 
        updated_at: updatedAt,
        quality_status: quality_status 
      });
    }
  );
});

module.exports = router;
